import { create<PERSON>out<PERSON>, z } from "@hono/zod-openapi";
import { UserProfileResponseSchema } from "../users/users.routes";
import { workspaceResponseSchema } from "../workspaces/workspaces.routes";

// =============================================================================
// BASE AUTH SCHEMAS
// =============================================================================

// Base user schema for auth responses
const baseUserSchema = z.object({
  id: z.string().describe("Unique user identifier from Supabase Auth"),
  email: z.string().describe("User's email address"),
  createdAt: z.string().describe("ISO timestamp when the user was created"),
}).openapi("User");

// Base auth session schema
const baseAuthSessionSchema = z.object({
  accessToken: z.string().describe("JWT access token for API authentication"),
  refreshToken: z.string().describe("Refresh token for obtaining new access tokens"),
  expiresAt: z.number().describe("Unix timestamp when the access token expires"),
}).openapi("AuthSession");

// Base password schema for reuse
const basePasswordSchema = z.string().min(6).describe("Password (minimum 6 characters)");

// Base token schema for reuse
const baseTokenSchema = z.string().describe("Authentication token");

// =============================================================================
// REQUEST SCHEMAS (extending/composing from base)
// =============================================================================

// Sign up request schema - comprehensive user registration
export const signUpRequestSchema = z.object({
  // Authentication fields
  email: z.string().email(),
  password: basePasswordSchema,
  confirmPassword: basePasswordSchema,
  
  // User profile fields
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  
  // Workspace creation fields
  companyName: z.string().min(1),
  companyType: z.enum(['individual', 'team', 'firm']),
  phone: z.string().optional(),
  licenseNumber: z.string().optional(),
  website: z.string().url().optional(),
  address: z.string().optional(),
  
  // Agreement fields
  termsAccepted: z.boolean(),
  marketingConsent: z.boolean().optional(),
}).openapi("SignUpRequest");

// Sign in request schema - minimal auth fields
export const signInRequestSchema = z.object({
  email: z.string().email().describe("User's email address"),
  password: basePasswordSchema,
}).openapi("SignInRequest", {
  example: {
    email: "<EMAIL>",
    password: "securePassword123"
  }
});

// Token-based request schemas
export const signOutRequestSchema = z.object({
  refreshToken: baseTokenSchema,
}).openapi("SignOutRequest");

export const refreshRequestSchema = z.object({
  refreshToken: baseTokenSchema,
}).openapi("RefreshRequest");

// Password reset request schemas
export const forgotPasswordRequestSchema = z.object({
  email: z.string().email(),
}).openapi("ForgotPasswordRequest");

export const resetPasswordRequestSchema = z.object({
  token: baseTokenSchema,
  newPassword: basePasswordSchema,
  confirmPassword: basePasswordSchema,
}).openapi("ResetPasswordRequest");

// =============================================================================
// RESPONSE SCHEMAS (composing base schemas)
// =============================================================================

// Core auth response schemas using base schemas
export const userSchema = baseUserSchema;
export const authSessionSchema = baseAuthSessionSchema;

// Comprehensive auth responses with related data
export const signUpResponseSchema = z.object({
  user: userSchema,
  workspace: workspaceResponseSchema,
  profile: UserProfileResponseSchema,
  session: authSessionSchema,
}).openapi("SignUpResponse");

export const signInResponseSchema = z.object({
  user: userSchema.describe("Authenticated user information"),
  session: authSessionSchema.describe("Authentication session tokens"),
  workspace: workspaceResponseSchema.describe("User's default workspace information"),
  profile: UserProfileResponseSchema.describe("User's profile within the workspace"),
}).openapi("SignInResponse");

// Simple success responses
export const signOutResponseSchema = z.object({
  success: z.boolean(),
}).openapi("SignOutResponse");

export const forgotPasswordResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
}).openapi("ForgotPasswordResponse");

export const resetPasswordResponseSchema = z.object({
  success: z.boolean(),
}).openapi("ResetPasswordResponse");

// Extended refresh response with full context
export const refreshResponseSchema = z.object({
  accessToken: z.string().describe("JWT access token for API authentication"),
  refreshToken: z.string().describe("Refresh token for obtaining new access tokens"),
  expiresAt: z.number().describe("Unix timestamp when the access token expires"),
  user: userSchema.describe("Authenticated user information"),
  workspace: workspaceResponseSchema.describe("User's default workspace information"),
  profile: UserProfileResponseSchema.describe("User's profile within the workspace"),
}).openapi("RefreshResponse");

// =============================================================================
// ROUTE DEFINITIONS
// =============================================================================

// Routes
export const signUpRoute = createRoute({
  method: "post",
  path: "/v1/auth/signup",
  request: {
    body: {
      content: {
        "application/json": {
          schema: signUpRequestSchema,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: signUpResponseSchema,
        },
      },
      description: "User registration with workspace creation",
    },
    400: {
      description: "Bad request - validation errors",
    },
    409: {
      description: "User already exists",
    },
  },
  tags: ["Authentication"],
});

export const signInRoute = createRoute({
  method: "post",
  path: "/v1/auth/signin",
  request: {
    body: {
      content: {
        "application/json": {
          schema: signInRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: signInResponseSchema,
        },
      },
      description: "Successful authentication. Returns user information, authentication tokens, default workspace details, and user profile within that workspace.",
    },
    401: {
      description: "Authentication failed - invalid email or password",
    },
    403: {
      description: "User account exists but has no access to any workspace, or associated workspace not found",
    },
  },
  tags: ["Authentication"],
});

export const signOutRoute = createRoute({
  method: "post",
  path: "/v1/auth/signout",
  request: {
    body: {
      content: {
        "application/json": {
          schema: signOutRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: signOutResponseSchema,
        },
      },
      description: "User logout",
    },
    400: {
      description: "Bad request",
    },
  },
  tags: ["Authentication"],
});

export const refreshRoute = createRoute({
  method: "post",
  path: "/v1/auth/refresh",
  request: {
    body: {
      content: {
        "application/json": {
          schema: refreshRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: refreshResponseSchema,
        },
      },
      description: "Token refreshed successfully. Returns refreshed authentication tokens along with user information, default workspace details, and user profile within that workspace.",
    },
    401: {
      description: "Invalid or expired refresh token",
    },
    403: {
      description: "User account exists but has no access to any workspace, or associated workspace not found",
    },
  },
  tags: ["Authentication"],
});

export const forgotPasswordRoute = createRoute({
  method: "post",
  path: "/v1/auth/forgot-password",
  request: {
    body: {
      content: {
        "application/json": {
          schema: forgotPasswordRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: forgotPasswordResponseSchema,
        },
      },
      description: "Password reset request",
    },
    400: {
      description: "Bad request",
    },
  },
  tags: ["Authentication"],
});

export const resetPasswordRoute = createRoute({
  method: "post",
  path: "/v1/auth/reset-password",
  request: {
    body: {
      content: {
        "application/json": {
          schema: resetPasswordRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: resetPasswordResponseSchema,
        },
      },
      description: "Password reset confirmation",
    },
    400: {
      description: "Bad request - invalid token or passwords don't match",
    },
  },
  tags: ["Authentication"],
});

export const callbackRoute = createRoute({
  method: "get",
  path: "/v1/auth/callback",
  request: {
    query: z.object({
      token: z.string().optional(),
      type: z.string().optional(),
      accessToken: z.string().optional(),
      refreshToken: z.string().optional(),
    }).openapi("AuthCallbackQuery"),
  },
  responses: {
    302: {
      description: "Redirect to dashboard or error page",
    },
    400: {
      description: "Invalid callback parameters",
    },
  },
  tags: ["Authentication"],
});

// =============================================================================
// ROUTER ASSEMBLY
// =============================================================================

import { createRouter } from "@/lib/create-app";
import * as controller from "./auth.controller";

export const authRouter = createRouter()
  .openapi(signUpRoute, controller.signUp)
  .openapi(signInRoute, controller.signIn)
  .openapi(signOutRoute, controller.signOut)
  .openapi(refreshRoute, controller.refresh)
  .openapi(forgotPasswordRoute, controller.forgotPassword)
  .openapi(resetPasswordRoute, controller.resetPassword)
  .openapi(callbackRoute, controller.callback);

// Export router as default for backward compatibility during migration
export default authRouter; 