import configureOpenAPI from "@/lib/configure-open-api";
import createApp from "@/lib/create-app";
import index from "@/routes/index.route";
import apiClient from "@/routes/api-client.route";
import { authRouter } from "@/routes/v1/auth/auth.routes";
import { filesRouter } from "@/routes/v1/files/files.routes";
import { usersRouter } from "@/routes/v1/users/users.routes";
import { listingsRouter } from "@/routes/v1/listings/listings.routes";
import { logsRouter } from "@/routes/v1/logs/logs.routes";
import { workspacesRouter } from "@/routes/v1/workspaces/workspaces.routes";

const app = createApp();

configureOpenAPI(app);

const routes = [
  index,
  apiClient,
  authRouter,
  usersRouter,
  filesRouter,
  listingsRouter,
  logsRouter,
  workspacesRouter,
] as const;

routes.forEach((route) => {
  app.route("/", route);
});

export type AppType = typeof routes[number];

export default app;
