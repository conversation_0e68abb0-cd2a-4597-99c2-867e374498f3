import type { Context } from "hono";
import { LogsService, type LogData } from "@/routes/v1/logs/logs.service";

// Re-export for backward compatibility
export type { LogData };

export class ApiLogger {
  /**
   * Log API request/response to database
   */
  static async log(data: LogData): Promise<void> {
    return LogsService.addLog(data);
  }

  /**
   * Get IP address from request
   */
  static getIpAddress(c: Context): string {
    // Check various headers for IP address
    const forwarded = c.req.header('x-forwarded-for');
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    
    const realIp = c.req.header('x-real-ip');
    if (realIp) {
      return realIp;
    }
    
    const cfConnectingIp = c.req.header('cf-connecting-ip');
    if (cfConnectingIp) {
      return cfConnectingIp;
    }
    
    // Fallback to connection remote address
    return 'unknown';
  }

  /**
   * Sanitize object by removing sensitive fields
   */
  private static sanitizeObject(obj: any): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item));
    }

    const sensitiveFields = [
      'password', 
      'confirmPassword', 
      'currentPassword', 
      'newPassword',
      'token', 
      'accessToken', 
      'refreshToken',
      'secret',
      'apiKey',
      'privateKey',
      'ssn',
      'socialSecurityNumber',
      'creditCard',
      'cardNumber',
      'cvv',
      'pin'
    ];

    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const lowerKey = key.toLowerCase();
      if (sensitiveFields.some(field => lowerKey.includes(field))) {
        result[key] = '[REDACTED]';
      } else {
        result[key] = this.sanitizeObject(value);
      }
    }
    return result;
  }

  /**
   * Sanitize request body for logging (remove sensitive data)
   */
  static sanitizeRequestBody(body: any, path: string): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    return this.sanitizeObject({ ...body });
  }

  /**
   * Sanitize response body for logging
   */
  static sanitizeResponseBody(body: any, statusCode: number): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    // Don't log large successful responses (like file downloads)
    if (statusCode >= 200 && statusCode < 300 && typeof body === 'object') {
      const bodyStr = JSON.stringify(body);
      if (bodyStr.length > 50000) { // 50KB limit
        return { 
          message: '[RESPONSE TOO LARGE]',
          size: bodyStr.length,
          type: Array.isArray(body) ? 'array' : 'object'
        };
      }
    }

    // For error responses and other cases, sanitize sensitive data
    return this.sanitizeObject(body);
  }

  /**
   * Check if path should be logged
   */
  static shouldLog(path: string, method: string): boolean {
    // Skip logging for certain paths
    const skipPaths = [
      '/health',
      '/doc',
      '/reference',
      '/favicon.ico',
      '/_log' // Don't log requests to the logging endpoints themselves
    ];

    // Skip OPTIONS requests (CORS preflight)
    if (method === 'OPTIONS') {
      return false;
    }

    return !skipPaths.some(skipPath => path.startsWith(skipPath));
  }
} 