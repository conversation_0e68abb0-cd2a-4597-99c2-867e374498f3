import type { User } from "@supabase/supabase-js";
import { supabase } from "@/db";

export interface AuthUser extends User {}

export class SupabaseAuth {
  /**
   * Sign up a new user with email and password
   */
  static async signUp(email: string, password: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: undefined // Don't require email confirmation in tests
      }
    });
    
    if (error) throw error;
    return data;
  }

  /**
   * Sign in an existing user with email and password
   */
  static async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) throw error;
    return data;
  }

  /**
   * Sign out the current user
   */
  static async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  }

  /**
   * Get the current authenticated user
   */
  static async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  }

  /**
   * Verify a JWT token and get the user
   */
  static async verifyToken(token: string) {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    if (error) throw error;
    return user;
  }

  /**
   * Reset password for a user
   */
  static async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email);
    if (error) throw error;
  }
}

export { supabase };
export default SupabaseAuth; 