# Centralized Middleware System

This directory contains a centralized middleware system for handling CORS and authentication across the application.

## Overview

The system provides automatic handling of:
- **CORS** (Cross-Origin Resource Sharing) for all endpoints
- **Authentication** based on endpoint patterns
- **Public endpoints** that don't require authentication
- **Optional authentication** endpoints

## Key Files

- `cors.ts` - Centralized CORS configuration
- `middleware-utils.ts` - Smart authentication and endpoint classification
- `create-app.ts` - Main app configuration with middleware

## Endpoint Types

### 1. Public Endpoints
Endpoints that are accessible without authentication:
- Health checks (`/health`, `/`)
- API documentation (`/doc`, `/reference`)
- Authentication endpoints (`/v1/auth/*`)
- All OPTIONS requests (CORS preflight)

### 2. Authenticated Endpoints
Endpoints that require user authentication:
- User management (`/v1/users/*`)
- File operations (`/v1/files/*`)
- Any other protected resources

### 3. Optional Authentication Endpoints
Endpoints where authentication is optional (user context available if authenticated):
- Currently none, but can be configured

## Usage Examples

### Basic Route Module Setup

```typescript
// For authenticated routes (most common)
import { createRouter } from "@/lib/create-app";
import { authenticatedEndpoint } from "@/lib/middleware-utils";

const router = createRouter()
  .openapi(route1, handler1)
  .openapi(route2, handler2);

// Apply authentication to all routes in this module
export default router.use("*", authenticatedEndpoint());
```

### Public Route Module Setup

```typescript
// For public routes
import { createRouter } from "@/lib/create-app";
import { publicEndpoint } from "@/lib/middleware-utils";

const router = createRouter()
  .openapi(publicRoute1, handler1)
  .openapi(publicRoute2, handler2);

// Apply public endpoint middleware (CORS only, no auth)
export default router.use("*", publicEndpoint());
```

### Mixed Route Module Setup

```typescript
// For modules with mixed access levels
import { createRouter } from "@/lib/create-app";
import { 
  publicEndpoint, 
  authenticatedEndpoint,
  optionalAuthEndpoint 
} from "@/lib/middleware-utils";

const router = createRouter()
  .openapi(publicRoute, publicHandler)
  .openapi(protectedRoute, protectedHandler)
  .openapi(optionalRoute, optionalHandler);

// Apply different middleware to different patterns
router.use("/public/*", publicEndpoint());
router.use("/protected/*", authenticatedEndpoint());
router.use("/optional/*", optionalAuthEndpoint());

export default router;
```

### Adding Custom Public Endpoints

```typescript
import { addPublicEndpoints } from "@/lib/middleware-utils";

// Add custom patterns for public endpoints
addPublicEndpoints([
  '/api/public/*',
  '/api/webhooks/*',
  /^\/api\/status\/.+$/,
  (path, method) => path.startsWith('/api/anonymous') && method === 'GET'
]);
```

### Environment-Specific CORS

The system automatically configures CORS based on environment variables defined in your `.env` file:

```env
# CORS Configuration
CORS_ORIGINS="http://localhost:8080,http://localhost:3000,http://localhost:3001"
CORS_CREDENTIALS=true
CORS_MAX_AGE=86400

# Production example:
# CORS_ORIGINS="https://your-production-domain.com,https://app.your-domain.com"
# CORS_CREDENTIALS=true
# CORS_MAX_AGE=3600
```

The system uses these environment variables:
- `CORS_ORIGINS`: Comma-separated list of allowed origins
- `CORS_CREDENTIALS`: Whether to allow credentials in CORS requests (default: true)
- `CORS_MAX_AGE`: CORS preflight cache duration in seconds (default: 86400 = 24 hours)

Different configurations per environment:

```typescript
// Development: Uses CORS_ORIGINS from .env
// Production: Uses CORS_ORIGINS from .env (with production domains)

// The system automatically applies the correct configuration
// based on NODE_ENV environment variable
```

## Manual Route-Level Control

For fine-grained control, you can use the convenience functions:

```typescript
import { 
  markAsPublic, 
  markAsAuthenticated, 
  markAsOptionalAuth 
} from "@/lib/middleware-utils";

// Mark specific route patterns
app.use('/api/special/public/*', markAsPublic());
app.use('/api/special/protected/*', markAsAuthenticated());
app.use('/api/special/optional/*', markAsOptionalAuth());
```

## Pattern Matching

The system supports multiple pattern types:

```typescript
// String patterns (exact match or prefix)
'/api/public'           // Matches /api/public and /api/public/*

// RegExp patterns
/^\/api\/v\d+\/public/  // Matches /api/v1/public, /api/v2/public, etc.

// Function patterns
(path, method) => path.includes('webhook') && method === 'POST'
```

## Benefits

1. **Centralized Configuration**: All CORS and auth logic in one place
2. **Automatic Classification**: Routes are automatically classified based on patterns
3. **Consistent Behavior**: All routes follow the same authentication flow
4. **Easy to Extend**: Simple to add new patterns or modify existing ones
5. **Type Safety**: Full TypeScript support with proper types
6. **Environment Aware**: Different configurations for dev/production

## Migration from Old System

Replace individual middleware applications:

```typescript
// OLD:
import { auth } from "@/middlewares/auth";
import { cors } from "hono/cors";

router.use("*", cors({ /* config */ }));
router.use("*", auth);

// NEW:
import { authenticatedEndpoint } from "@/lib/middleware-utils";

router.use("*", authenticatedEndpoint());
```

The new system handles both CORS and authentication automatically based on the endpoint type. 