export interface ListingResponse {
  id: string;
  workspace_id: string;
  created_by: string;
  assigned_to?: string;
  business_name: string;
  industry: string;
  asking_price?: number;
  cash_flow_sde?: number;
  annual_revenue?: number;
  status: string;
  general_location?: string;
  year_established?: number;
  employees?: number;
  owner_hours_week?: number;
  date_listed?: string;
  days_listed?: number;
  title?: string;
  description?: string;
  price?: number;
  team_visibility: string;
  created_at: string;
  updated_at: string;
  details?: ListingDetailsResponse;
}

export interface ListingDetailsResponse {
  id: string;
  listing_id: string;
  business_description?: string;
  brief_description?: string;
  financial_details: {
    revenue_2023?: number;
    ebitda?: number;
    assets_included?: string[];
    inventory_value?: number;
    additional_financial_info?: Record<string, any>;
  };
  operations: {
    business_model?: string;
    key_features?: string[];
    competitive_advantages?: string[];
    operational_details?: Record<string, any>;
  };
  growth_opportunities?: string[];
  reason_for_sale?: string;
  training_period?: string;
  support_type?: string;
  financing_available: boolean;
  equipment_highlights?: string[];
  supplier_relationships?: string;
  real_estate_status?: string;
  lease_details: {
    lease_terms?: string;
    monthly_rent?: number;
    lease_expiration?: string;
    renewal_options?: string;
    landlord_info?: Record<string, any>;
  };
  created_at: string;
  updated_at: string;
}

export interface StatusHistoryResponse {
  id: string;
  from_status?: string;
  to_status: string;
  reason?: string;
  notes?: string;
  changed_by: string;
  changed_by_name?: string;
  created_at: string;
}

export interface ListingStatsResponse {
  total: number;
  active: number;
  underContract: number;
  sold: number;
  avgAskingPrice?: number;
  avgDaysListed?: number;
}

export interface BulkCreateResult {
  created: ListingResponse[];
  failed: Array<{
    index: number;
    error: string;
    data: Record<string, any>;
  }>;
}

export enum ListingStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  UNDER_CONTRACT = 'under_contract',
  SOLD = 'sold',
  CONFIDENTIAL = 'confidential',
  EXPIRED = 'expired',
  WITHDRAWN = 'withdrawn',
}

export enum RealEstateStatus {
  OWNED = 'owned',
  LEASED = 'leased',
  INCLUDED = 'included',
  NOT_INCLUDED = 'not_included',
  NEGOTIABLE = 'negotiable',
}

export enum TeamVisibility {
  ALL = 'all',
  ASSIGNED_ONLY = 'assigned_only',
  ADMINS_ONLY = 'admins_only',
} 