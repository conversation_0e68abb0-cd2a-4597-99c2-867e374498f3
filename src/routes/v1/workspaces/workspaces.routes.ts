import { createRoute, z } from "@hono/zod-openapi";
import { 
  successResponseSchema, 
  deleteResponseSchema, 
  paginationSchema 
} from "@/lib/commonApiSchema";
import { authMiddleware } from "@/middlewares/auth";

// =============================================================================
// BASE WORKSPACE SCHEMAS
// =============================================================================

// Base workspace schema mapped from database structure
const baseWorkspaceSchema = z.object({
  companyName: z.string().min(1, "Company name is required"),
  companyType: z.enum(['individual', 'team', 'firm']).default('team'),
  subscriptionPlan: z.enum(['trial', 'basic', 'pro', 'enterprise']).default('trial'),
  domain: z.string().optional(),
  logoUrl: z.string().optional(),
  primaryColor: z.string().default('#3B82F6'),
  address: z.string().optional(),
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/, "Invalid phone format").optional(),
  website: z.string().url("Invalid URL format").optional(),
  licenseNumber: z.string().optional(),
  specialties: z.array(z.string()).optional(),
  targetMarkets: z.array(z.string()).optional(),
  status: z.enum(['trial', 'active', 'suspended', 'cancelled']).default('trial'),
  onboardingCompleted: z.boolean().default(false),
  onboardingStep: z.number().int().default(1),
}).openapi("BaseWorkspace");

// Base workspace invitation schema mapped from database structure
const baseWorkspaceInvitationSchema = z.object({
  workspaceId: z.string().uuid(),
  email: z.string().email("Invalid email format"),
  role: z.enum(['owner', 'admin', 'member', 'viewer']),
  invitedBy: z.string().uuid(),
}).openapi("BaseWorkspaceInvitation");

// Reusable enums for consistency
const companyTypeEnum = z.enum(['individual', 'team', 'firm']);
const subscriptionPlanEnum = z.enum(['trial', 'basic', 'pro', 'enterprise']);
const workspaceStatusEnum = z.enum(['trial', 'active', 'suspended', 'cancelled']);
const roleEnum = z.enum(['owner', 'admin', 'member', 'viewer']);

// =============================================================================
// REQUEST SCHEMAS (extending/reducing from base)
// =============================================================================

// Workspace request schemas
export const createWorkspaceRequestSchema = baseWorkspaceSchema
  .openapi("CreateWorkspaceRequest");

export const updateWorkspaceRequestSchema = createWorkspaceRequestSchema
  .partial()
  .openapi("UpdateWorkspaceRequest");

// Workspace invitation request schemas
export const createWorkspaceInvitationRequestSchema = z.object({
  email: z.string().email("Invalid email format"),
  role: roleEnum,
}).openapi("CreateWorkspaceInvitationRequest");

export const updateWorkspaceInvitationRequestSchema = z.object({
  role: roleEnum.optional(),
}).openapi("UpdateWorkspaceInvitationRequest");

// =============================================================================
// RESPONSE SCHEMAS (extending base with computed fields)
// =============================================================================

// Workspace response schema - base + system fields
export const workspaceResponseSchema = baseWorkspaceSchema
  .extend({
    // System-generated fields
    id: z.string().uuid(),
    trialEndsAt: z.string().optional(),
    createdAt: z.string(),
    updatedAt: z.string(),
  })
  .openapi("Workspace");

// Workspace invitation response schema - base + computed fields
export const workspaceInvitationResponseSchema = baseWorkspaceInvitationSchema
  .extend({
    // System-generated fields
    id: z.string().uuid(),
    status: z.string().optional(),
    expiresAt: z.string().optional(),
    createdAt: z.string(),
    updatedAt: z.string(),
    
    // Computed fields
    canResend: z.boolean().optional(),
    isExpired: z.boolean().optional(),
  })
  .openapi("WorkspaceInvitation");

// =============================================================================
// REUSABLE RESPONSE WRAPPERS
// =============================================================================

// Workspace response wrappers
export const workspaceUpdateResponseSchema = z.object({
  success: z.boolean(),
  data: workspaceResponseSchema,
  updatedAt: z.string(),
  changesApplied: z.array(z.string()).optional(),
}).openapi("WorkspaceUpdateResponse");

export const workspaceListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(workspaceResponseSchema),
  pagination: paginationSchema,
}).openapi("WorkspaceListResponse");

export const singleWorkspaceResponseSchema = z.object({
  success: z.boolean(),
  data: workspaceResponseSchema,
}).openapi("SingleWorkspaceResponse");

// Workspace invitation response wrappers
export const workspaceInvitationListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(workspaceInvitationResponseSchema),
  pagination: paginationSchema,
}).openapi("WorkspaceInvitationListResponse");

export const singleWorkspaceInvitationResponseSchema = z.object({
  success: z.boolean(),
  data: workspaceInvitationResponseSchema,
}).openapi("SingleWorkspaceInvitationResponse");

// =============================================================================
// ROUTE DEFINITIONS
// =============================================================================

// Workspace management routes
export const getWorkspaceRoute = createRoute({
  method: "get",
  path: "/v1/workspaces/current",
  summary: "Get current workspace",
  description: "Get the current workspace information",
  middleware: [authMiddleware],
  responses: {
    200: {
      content: {
        "application/json": {
          schema: singleWorkspaceResponseSchema,
        },
      },
      description: "Current workspace retrieved successfully",
    },
    401: {
      description: "Authentication required",
    },
    404: {
      description: "Workspace not found",
    },
  },
  tags: ["Workspaces"],
});

export const updateWorkspaceRoute = createRoute({
  method: "put",
  path: "/v1/workspaces/current",
  summary: "Update current workspace",
  description: "Update the current workspace information",
  middleware: [authMiddleware],
  request: {
    body: {
      content: {
        "application/json": {
          schema: updateWorkspaceRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: workspaceUpdateResponseSchema,
        },
      },
      description: "Workspace updated successfully",
    },
    400: {
      description: "Invalid request data",
    },
    401: {
      description: "Authentication required",
    },
    403: {
      description: "Insufficient permissions",
    },
    404: {
      description: "Workspace not found",
    },
  },
  tags: ["Workspaces"],
});

// Workspace invitation routes
export const listWorkspaceInvitationsRoute = createRoute({
  method: "get",
  path: "/v1/workspaces/invitations",
  summary: "List workspace invitations",
  description: "Get all invitations for the current workspace",
  middleware: [authMiddleware],
  request: {
    query: z.object({
      page: z.string().regex(/^\d+$/).transform(Number).optional().default("1"),
      limit: z.string().regex(/^\d+$/).transform(Number).optional().default("20"),
      status: z.string().optional(),
      role: roleEnum.optional(),
    }).openapi("InvitationsQuery"),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: workspaceInvitationListResponseSchema,
        },
      },
      description: "Workspace invitations retrieved successfully",
    },
    401: {
      description: "Authentication required",
    },
    403: {
      description: "Insufficient permissions",
    },
  },
  tags: ["Workspaces", "Invitations"],
});

export const createWorkspaceInvitationRoute = createRoute({
  method: "post",
  path: "/v1/workspaces/invitations",
  summary: "Create workspace invitation",
  description: "Send an invitation to join the workspace",
  middleware: [authMiddleware],
  request: {
    body: {
      content: {
        "application/json": {
          schema: createWorkspaceInvitationRequestSchema,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: singleWorkspaceInvitationResponseSchema,
        },
      },
      description: "Workspace invitation created successfully",
    },
    400: {
      description: "Invalid request data",
    },
    401: {
      description: "Authentication required",
    },
    403: {
      description: "Insufficient permissions",
    },
    409: {
      description: "User already invited or exists in workspace",
    },
  },
  tags: ["Workspaces", "Invitations"],
});

export const updateWorkspaceInvitationRoute = createRoute({
  method: "put",
  path: "/v1/workspaces/invitations/{invitation_id}",
  summary: "Update workspace invitation",
  description: "Update an existing workspace invitation",
  middleware: [authMiddleware],
  request: {
    params: z.object({
      invitationId: z.string().uuid(),
    }).openapi("InvitationParams"),
    body: {
      content: {
        "application/json": {
          schema: updateWorkspaceInvitationRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: singleWorkspaceInvitationResponseSchema,
        },
      },
      description: "Workspace invitation updated successfully",
    },
    400: {
      description: "Invalid request data",
    },
    401: {
      description: "Authentication required",
    },
    403: {
      description: "Insufficient permissions",
    },
    404: {
      description: "Invitation not found",
    },
  },
  tags: ["Workspaces", "Invitations"],
});

export const deleteWorkspaceInvitationRoute = createRoute({
  method: "delete",
  path: "/v1/workspaces/invitations/{invitation_id}",
  summary: "Delete workspace invitation",
  description: "Cancel/delete a workspace invitation",
  middleware: [authMiddleware],
  request: {
    params: z.object({
      invitationId: z.string().uuid(),
    }).openapi("DeleteInvitationParams"),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: deleteResponseSchema,
        },
      },
      description: "Workspace invitation deleted successfully",
    },
    401: {
      description: "Authentication required",
    },
    403: {
      description: "Insufficient permissions",
    },
    404: {
      description: "Invitation not found",
    },
  },
  tags: ["Workspaces", "Invitations"],
});

export const resendWorkspaceInvitationRoute = createRoute({
  method: "post",
  path: "/v1/workspaces/invitations/{invitation_id}/resend",
  summary: "Resend workspace invitation",
  description: "Resend a workspace invitation email",
  middleware: [authMiddleware],
  request: {
    params: z.object({
      invitationId: z.string().uuid(),
    }).openapi("ResendInvitationParams"),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: singleWorkspaceInvitationResponseSchema,
        },
      },
      description: "Workspace invitation resent successfully",
    },
    401: {
      description: "Authentication required",
    },
    403: {
      description: "Insufficient permissions",
    },
    404: {
      description: "Invitation not found",
    },
    429: {
      description: "Rate limit exceeded",
    },
  },
  tags: ["Workspaces", "Invitations"],
});

// =============================================================================
// ROUTER ASSEMBLY
// =============================================================================

import { createRouter } from "@/lib/create-app";
import * as handlers from "./workspaces.controller";

export const workspacesRouter = createRouter()
  .openapi(getWorkspaceRoute, handlers.getWorkspace)
  .openapi(updateWorkspaceRoute, handlers.updateWorkspace)
  .openapi(listWorkspaceInvitationsRoute, handlers.listWorkspaceInvitations)
  .openapi(createWorkspaceInvitationRoute, handlers.createWorkspaceInvitation)
  .openapi(updateWorkspaceInvitationRoute, handlers.updateWorkspaceInvitation)
  .openapi(deleteWorkspaceInvitationRoute, handlers.deleteWorkspaceInvitation)
  .openapi(resendWorkspaceInvitationRoute, handlers.resendWorkspaceInvitation);

// Export router as default for backward compatibility during migration
export default workspacesRouter; 