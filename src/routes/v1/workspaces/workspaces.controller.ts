import { Context } from "hono";
import { getAuthenticatedUser, getUserWorkspace } from "@/lib/auth-utils";
import { WorkspacesService } from "./workspaces.service";

export async function getWorkspace(c: Context) {
  try {
    const user = await getAuthenticatedUser(c);
    const workspace = await getUserWorkspace(c);
    
    const workspaceData = await WorkspacesService.getWorkspace(workspace.id);

    return c.json({
      success: true,
      data: workspaceData,
    });
  } catch (error) {
    console.error("Error getting workspace:", error);
    return c.json(
      { 
        success: false, 
        error: "Failed to retrieve workspace" 
      },
      500
    );
  }
}

export async function updateWorkspace(c: Context) {
  try {
    const user = await getAuthenticatedUser(c);
    const workspace = await getUserWorkspace(c);
    const body = await c.req.json();

    // Check permissions - only owners and admins can update workspace
    if (!user.profile?.role || !["owner", "admin"].includes(user.profile.role)) {
      return c.json(
        { 
          success: false, 
          error: "Insufficient permissions to update workspace" 
        },
        403
      );
    }

    const updatedWorkspace = await WorkspacesService.updateWorkspace(
      workspace.id,
      body
    );

    return c.json({
      success: true,
      data: updatedWorkspace,
      updatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error updating workspace:", error);
    return c.json(
      { 
        success: false, 
        error: "Failed to update workspace" 
      },
      500
    );
  }
}

export async function listWorkspaceInvitations(c: Context) {
  try {
    const user = await getAuthenticatedUser(c);
    const workspace = await getUserWorkspace(c);
    
    // Check permissions - only owners and admins can view invitations
    if (!user.profile?.role || !["owner", "admin"].includes(user.profile.role)) {
      return c.json(
        { 
          success: false, 
          error: "Insufficient permissions to view invitations" 
        },
        403
      );
    }

    const query = c.req.query();
    const page = parseInt(query.page || "1");
    const limit = parseInt(query.limit || "20");
    const status = query.status;
    const role = query.role;

    const invitations = await WorkspacesService.listInvitations(
      workspace.id,
      { page, limit, status, role }
    );

    return c.json({
      success: true,
      data: invitations.data,
      pagination: invitations.pagination,
    });
  } catch (error) {
    console.error("Error listing workspace invitations:", error);
    return c.json(
      { 
        success: false, 
        error: "Failed to retrieve invitations" 
      },
      500
    );
  }
}

export async function createWorkspaceInvitation(c: Context) {
  try {
    const user = await getAuthenticatedUser(c);
    const workspace = await getUserWorkspace(c);
    const body = await c.req.json();

    // Check permissions - only owners and admins can create invitations
    if (!user.profile?.role || !["owner", "admin"].includes(user.profile.role)) {
      return c.json(
        { 
          success: false, 
          error: "Insufficient permissions to create invitations" 
        },
        403
      );
    }

    const invitation = await WorkspacesService.createInvitation(
      workspace.id,
      user.id,
      body
    );

    return c.json({
      success: true,
      data: invitation,
    }, 201);
  } catch (error) {
    console.error("Error creating workspace invitation:", error);
    
    // Handle specific errors
    if (error instanceof Error) {
      if (error.message.includes("already exists")) {
        return c.json(
          { 
            success: false, 
            error: "User already invited or exists in workspace" 
          },
          409
        );
      }
    }
    
    return c.json(
      { 
        success: false, 
        error: "Failed to create invitation" 
      },
      500
    );
  }
}

export async function updateWorkspaceInvitation(c: Context) {
  try {
    const user = await getAuthenticatedUser(c);
    const workspace = await getUserWorkspace(c);
    const invitationId = c.req.param("invitation_id");
    const body = await c.req.json();

    // Check permissions - only owners and admins can update invitations
    if (!user.profile?.role || !["owner", "admin"].includes(user.profile.role)) {
      return c.json(
        { 
          success: false, 
          error: "Insufficient permissions to update invitations" 
        },
        403
      );
    }

    const invitation = await WorkspacesService.updateInvitation(
      invitationId,
      workspace.id,
      body
    );

    return c.json({
      success: true,
      data: invitation,
    });
  } catch (error) {
    console.error("Error updating workspace invitation:", error);
    return c.json(
      { 
        success: false, 
        error: "Failed to update invitation" 
      },
      500
    );
  }
}

export async function deleteWorkspaceInvitation(c: Context) {
  try {
    const user = await getAuthenticatedUser(c);
    const workspace = await getUserWorkspace(c);
    const invitationId = c.req.param("invitation_id");

    // Check permissions - only owners and admins can delete invitations
    if (!user.profile?.role || !["owner", "admin"].includes(user.profile.role)) {
      return c.json(
        { 
          success: false, 
          error: "Insufficient permissions to delete invitations" 
        },
        403
      );
    }

    await WorkspacesService.deleteInvitation(invitationId, workspace.id);

    return c.json({
      success: true,
      message: "Workspace invitation deleted successfully",
      deletedId: invitationId,
      deletedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error deleting workspace invitation:", error);
    return c.json(
      { 
        success: false, 
        error: "Failed to delete invitation" 
      },
      500
    );
  }
}

export async function resendWorkspaceInvitation(c: Context) {
  try {
    const user = await getAuthenticatedUser(c);
    const workspace = await getUserWorkspace(c);
    const invitationId = c.req.param("invitation_id");

    // Check permissions - only owners and admins can resend invitations
    if (!user.profile?.role || !["owner", "admin"].includes(user.profile.role)) {
      return c.json(
        { 
          success: false, 
          error: "Insufficient permissions to resend invitations" 
        },
        403
      );
    }

    const invitation = await WorkspacesService.resendInvitation(
      invitationId,
      workspace.id
    );

    return c.json({
      success: true,
      data: invitation,
    });
  } catch (error) {
    console.error("Error resending workspace invitation:", error);
    
    // Handle rate limiting
    if (error instanceof Error && error.message.includes("rate limit")) {
      return c.json(
        { 
          success: false, 
          error: "Rate limit exceeded. Please wait before resending." 
        },
        429
      );
    }
    
    return c.json(
      { 
        success: false, 
        error: "Failed to resend invitation" 
      },
      500
    );
  }
} 