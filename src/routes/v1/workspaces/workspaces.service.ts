import { eq, and, desc, sql } from "drizzle-orm";
import db from "@/db";
import { workspaces, workspaceInvitations } from "@/db/schema";

export class WorkspacesService {
  // Workspace operations
  static async getWorkspace(workspaceId: string) {
    const workspace = await db
      .select()
      .from(workspaces)
      .where(eq(workspaces.id, workspaceId))
      .limit(1);

    if (!workspace[0]) {
      throw new Error("Workspace not found");
    }

    return workspace[0];
  }

  static async updateWorkspace(workspaceId: string, data: any) {
    const updatedWorkspace = await db
      .update(workspaces)
      .set({
        ...data,
        updatedAt: new Date().toISOString(),
      })
      .where(eq(workspaces.id, workspaceId))
      .returning();

    if (!updatedWorkspace[0]) {
      throw new Error("Failed to update workspace");
    }

    return updatedWorkspace[0];
  }

  // Invitation operations
  static async listInvitations(
    workspaceId: string,
    options: {
      page: number;
      limit: number;
      status?: string;
      role?: string;
    }
  ) {
    const { page, limit, status, role } = options;
    const offset = (page - 1) * limit;

    let query = db
      .select()
      .from(workspaceInvitations)
      .where(eq(workspaceInvitations.workspaceId, workspaceId))
      .orderBy(desc(workspaceInvitations.createdAt))
      .limit(limit)
      .offset(offset);

    // Apply filters if provided
    // Note: This is a simplified example - you would add proper filtering logic

    const invitations = await query;

    // Get total count for pagination
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(workspaceInvitations)
      .where(eq(workspaceInvitations.workspaceId, workspaceId));

    const total = totalResult[0]?.count || 0;
    const pages = Math.ceil(total / limit);

    return {
      data: invitations.map(invitation => ({
        ...invitation,
        canResend: this.canResendInvitation(invitation),
        isExpired: this.isInvitationExpired(invitation),
      })),
      pagination: {
        page,
        limit,
        total,
        pages,
      },
    };
  }

  static async createInvitation(
    workspaceId: string,
    invitedBy: string,
    data: { email: string; role: string }
  ) {
    // Check if user already exists or has pending invitation
    const existingInvitation = await db
      .select()
      .from(workspaceInvitations)
      .where(
        and(
          eq(workspaceInvitations.workspaceId, workspaceId),
          eq(workspaceInvitations.email, data.email)
        )
      )
      .limit(1);

    if (existingInvitation[0]) {
      throw new Error("User already invited or exists in workspace");
    }

    // Create new invitation
    const invitation = await db
      .insert(workspaceInvitations)
      .values({
        workspaceId,
        email: data.email,
        role: data.role,
        invitedBy,
        token: this.generateInvitationToken(),
        expiresAt: this.getExpirationDate(),
      })
      .returning();

    if (!invitation[0]) {
      throw new Error("Failed to create invitation");
    }

    // TODO: Send invitation email
    // await this.sendInvitationEmail(invitation[0]);

    return {
      ...invitation[0],
      canResend: true,
      isExpired: false,
    };
  }

  static async updateInvitation(
    invitationId: string,
    workspaceId: string,
    data: { role?: string }
  ) {
    const updatedInvitation = await db
      .update(workspaceInvitations)
      .set(data)
      .where(
        and(
          eq(workspaceInvitations.id, invitationId),
          eq(workspaceInvitations.workspaceId, workspaceId)
        )
      )
      .returning();

    if (!updatedInvitation[0]) {
      throw new Error("Invitation not found or failed to update");
    }

    return {
      ...updatedInvitation[0],
      canResend: this.canResendInvitation(updatedInvitation[0]),
      isExpired: this.isInvitationExpired(updatedInvitation[0]),
    };
  }

  static async deleteInvitation(invitationId: string, workspaceId: string) {
    const deletedInvitation = await db
      .delete(workspaceInvitations)
      .where(
        and(
          eq(workspaceInvitations.id, invitationId),
          eq(workspaceInvitations.workspaceId, workspaceId)
        )
      )
      .returning();

    if (!deletedInvitation[0]) {
      throw new Error("Invitation not found");
    }

    return deletedInvitation[0];
  }

  static async resendInvitation(invitationId: string, workspaceId: string) {
    // Check rate limiting
    const invitation = await db
      .select()
      .from(workspaceInvitations)
      .where(
        and(
          eq(workspaceInvitations.id, invitationId),
          eq(workspaceInvitations.workspaceId, workspaceId)
        )
      )
      .limit(1);

    if (!invitation[0]) {
      throw new Error("Invitation not found");
    }

    // Simple rate limiting check (can be enhanced)
    const lastUpdated = new Date(invitation[0].updatedAt);
    const now = new Date();
    const timeDiff = now.getTime() - lastUpdated.getTime();
    const hoursDiff = timeDiff / (1000 * 60 * 60);

    if (hoursDiff < 1) {
      throw new Error("Rate limit exceeded. Wait 1 hour between resends.");
    }

    // Update invitation with new token and expiration
    const updatedInvitation = await db
      .update(workspaceInvitations)
      .set({
        token: this.generateInvitationToken(),
        expiresAt: this.getExpirationDate(),
        updatedAt: new Date().toISOString(),
      })
      .where(eq(workspaceInvitations.id, invitationId))
      .returning();

    // TODO: Send invitation email
    // await this.sendInvitationEmail(updatedInvitation[0]);

    return {
      ...updatedInvitation[0],
      canResend: true,
      isExpired: false,
    };
  }

  // Helper methods
  private static generateInvitationToken(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  private static getExpirationDate(): string {
    const date = new Date();
    date.setDate(date.getDate() + 7); // 7 days from now
    return date.toISOString();
  }

  private static canResendInvitation(invitation: any): boolean {
    // Can resend if not expired and status is pending
    return !this.isInvitationExpired(invitation) && 
           invitation.status !== "accepted";
  }

  private static isInvitationExpired(invitation: any): boolean {
    if (!invitation.expiresAt) return false;
    return new Date(invitation.expiresAt) < new Date();
  }

  // TODO: Implement email sending
  // private static async sendInvitationEmail(invitation: any) {
  //   // Send invitation email logic
  // }
} 