import { create<PERSON>out<PERSON>, z } from "@hono/zod-openapi";
import { authMiddleware } from "@/middlewares/auth";

// =============================================================================
// BASE USER PROFILE SCHEMAS
// =============================================================================

// Base user profile schema mapped from database structure
const baseUserProfileSchema = z.object({
  workspaceId: z.string().uuid().optional(),
  userId: z.string().uuid().optional(),
  email: z.string().email("Invalid email format"),
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  displayName: z.string().optional(),
  bio: z.string().optional(),
  role: z.enum(['owner', 'admin', 'member', 'viewer']).optional(),
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/, "Invalid phone format").optional(),
  licenseNumber: z.string().optional(),
  avatarUrl: z.string().optional(),
  specialties: z.array(z.string()).optional(),
  isActive: z.boolean().default(true),
  preferences: z.record(z.any()).optional(),
}).openapi("BaseUserProfile");

// Base preferences schema for reuse
const basePreferencesSchema = z.object({
  notifications: z.object({
    emailNotifications: z.boolean(),
    pushNotifications: z.boolean(),
    listingUpdates: z.boolean(),
    teamUpdates: z.boolean(),
    systemUpdates: z.boolean(),
  }).optional(),
  display: z.object({
    timezone: z.string(),
    dateFormat: z.string(),
    currency: z.string(),
    language: z.string(),
  }).optional(),
  privacy: z.object({
    profileVisibility: z.enum(['team', 'public', 'private']),
    contactVisibility: z.enum(['team', 'public', 'private']),
  }).optional(),
}).openapi("UserPreferences");

// =============================================================================
// REQUEST SCHEMAS (extending/reducing from base)
// =============================================================================

// Profile update request schema - only editable fields
export const UpdateProfileRequestSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  displayName: z.string().optional(),
  bio: z.string().optional(),
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/, "Invalid phone format").optional(),
  licenseNumber: z.string().optional(),
  avatarUrl: z.string().url().optional(),
  specialties: z.array(z.string()).optional(),
  preferences: basePreferencesSchema.optional(),
}).openapi("UpdateProfileRequest");

// Email verification request schema
export const VerifyEmailRequestSchema = z.object({
  token: z.string(),
  email: z.string().email(),
}).openapi("VerifyEmailRequest");

// =============================================================================
// RESPONSE SCHEMAS (extending base with computed fields)
// =============================================================================

// Full user profile response schema
export const UserProfileResponseSchema = baseUserProfileSchema
  .extend({
    // System-generated fields
    id: z.string().uuid(),
    invitedAt: z.string().optional(),
    joinedAt: z.string().optional(),
    invitedBy: z.string().uuid().optional(),
    lastLoginAt: z.string().optional(),
    createdAt: z.string(),
    updatedAt: z.string(),
  })
  .openapi("UserProfileResponse");

// Extended update response with operation details
export const UpdateProfileResponseSchema = z.object({
  id: z.string(),
  workspaceId: z.string(),
  email: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  role: z.string(),
  phone: z.string().optional(),
  licenseNumber: z.string().optional(),
  bio: z.string().optional(),
  avatarUrl: z.string().optional(),
  specialties: z.array(z.string()),
  isActive: z.boolean(),
  preferences: z.any(),
  updatedAt: z.string(),
  
  // Response metadata for operations
  operationsCompleted: z.object({
    profileUpdated: z.boolean(),
    passwordChanged: z.boolean().optional(),
    emailChangeInitiated: z.boolean().optional(),
    emailVerificationSent: z.boolean().optional(),
    avatarUpdated: z.boolean().optional(),
  }),
  
  // Warnings or next steps
  requiresReauth: z.boolean().optional(),
  warnings: z.array(z.string()).optional(),
}).openapi("UpdateProfileResponse");

// Email verification response schema
export const VerifyEmailResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  emailUpdated: z.boolean(),
}).openapi("VerifyEmailResponse");

// =============================================================================
// REUSABLE RESPONSE WRAPPERS (not exported - used internally)
// =============================================================================

// For potential future use - batch operations, admin endpoints, etc.
const userProfileListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(UserProfileResponseSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    pages: z.number(),
  }),
}).openapi("UserProfileListResponse");

const createUserProfileRequestSchema = baseUserProfileSchema
  .openapi("CreateUserProfileRequest");

const userProfileUpdateResponseSchema = z.object({
  success: z.boolean(),
  data: UserProfileResponseSchema,
  updatedAt: z.string(),
  changesApplied: z.array(z.string()).optional(),
}).openapi("UserProfileUpdateResponse");

// =============================================================================
// TYPE EXPORTS FOR BACKWARD COMPATIBILITY
// =============================================================================

// User preferences interface for TypeScript typing
export interface UserPreferences {
  notifications: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    listingUpdates: boolean;
    teamUpdates: boolean;
    systemUpdates: boolean;
  };
  display: {
    timezone: string;
    dateFormat: string;
    currency: string;
    language: string;
  };
  privacy: {
    profileVisibility: 'team' | 'public' | 'private';
    contactVisibility: 'team' | 'public' | 'private';
  };
}

// =============================================================================
// ROUTE DEFINITIONS
// =============================================================================

// GET /users/profile route
export const getUserProfileRoute = createRoute({
  tags: ["Users"],
  method: "get",
  path: "/profile",
  summary: "Get current user profile",
  description: "Get current user profile with workspace context",
  middleware: [authMiddleware],
  responses: {
    200: {
      content: {
        "application/json": {
          schema: UserProfileResponseSchema,
        },
      },
      description: "User profile retrieved successfully",
    },
    401: {
      description: "Authentication required",
    },
    404: {
      description: "User profile not found",
    },
  },
});

// PUT /users/profile route
export const updateUserProfileRoute = createRoute({
  tags: ["Users"],
  method: "put",
  path: "/profile",
  summary: "Update user profile",
  description: "Update user profile information (centralized endpoint for all profile updates)",
  middleware: [authMiddleware],
  request: {
    body: {
      content: {
        "application/json": {
          schema: UpdateProfileRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: UpdateProfileResponseSchema,
        },
      },
      description: "Profile updated successfully",
    },
    400: {
      description: "Invalid request data",
    },
    401: {
      description: "Authentication required",
    },
    404: {
      description: "User profile not found",
    },
  },
});

// POST /users/verify-email route
export const verifyEmailRoute = createRoute({
  tags: ["Users"],
  method: "post",
  path: "/verify-email",
  summary: "Verify new email address",
  description: "Verify new email address (called after email change via PUT /users/profile)",
  request: {
    body: {
      content: {
        "application/json": {
          schema: VerifyEmailRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: VerifyEmailResponseSchema,
        },
      },
      description: "Email verification result",
    },
    400: {
      description: "Invalid verification token or email",
    },
  },
});

// =============================================================================
// ROUTER ASSEMBLY
// =============================================================================

import { createRouter } from "@/lib/create-app";
import * as handlers from "./users.controller";

export const usersRouter = createRouter()
  .openapi(getUserProfileRoute, handlers.getUserProfile)
  .openapi(updateUserProfileRoute, handlers.updateUserProfile)
  .openapi(verifyEmailRoute, handlers.verifyEmail);

// Export router as default for backward compatibility during migration
export default usersRouter;