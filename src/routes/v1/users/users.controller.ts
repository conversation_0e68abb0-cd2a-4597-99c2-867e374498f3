import type { Context } from "hono";
import { getAuthenticatedUser, getUserWorkspace } from "@/lib/auth-utils";
import { UsersService } from "./users.service";
import { UpdateProfileRequestSchema } from "./users.routes";

export async function getUserProfile(c: Context) {
  const user = getAuthenticatedUser(c);
  const workspace = getUserWorkspace(c);

  const profile = await UsersService.getUserProfile(user.id, workspace.id);

  return c.json(profile, 200);
}

export async function updateUserProfile(c: Context) {
  const user = getAuthenticatedUser(c);
  const workspace = getUserWorkspace(c);
  
  // Validate the request body manually
  const body = await c.req.json();
  const result = UpdateProfileRequestSchema.safeParse(body);
  
  if (!result.success) {
    return c.json({ error: "Validation failed", details: result.error.errors }, 400);
  }
  
  const updateData = result.data;

  const updatedProfile = await UsersService.updateUserProfile(
    user.id,
    workspace.id,
    updateData
  );

  return c.json(updatedProfile, 200);
}

export async function verifyEmail(c: Context) {
  const body = await c.req.json();
  const { token, email } = body;

  const result = await UsersService.verifyEmailChange(token, email);

  return c.json(result, 200);
} 