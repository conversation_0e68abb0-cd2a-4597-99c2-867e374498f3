import { describe, it, expect, beforeAll, afterAll } from "vitest";
import { testClient } from "hono/testing";
import app from "@/app";

// describe("User Profile APIs - Integration Tests", () => {
//   const client = testClient(app);

//   describe("Authentication Required", () => {
//     it("should require authentication for GET /v1/users/profile", async () => {
//       const res = await client.v1.users.profile.$get();
//       expect(res.status).toBe(401);
//     });

//     it("should require authentication for PUT /v1/users/profile", async () => {
//       const res = await client.v1.users.profile.$put({
//         json: {
//           first_name: "<PERSON>",
//           last_name: "<PERSON><PERSON>"
//         }
//       });
//       expect(res.status).toBe(401);
//     });

//     it("should handle POST /v1/users/verify-email without auth", async () => {
//       const res = await client.v1.users["verify-email"].$post({
//         json: {
//           token: "dummy-token",
//           email: "<EMAIL>"
//         }
//       });
//       // Should not return 404 (endpoint exists)
//       expect(res.status).not.toBe(404);
      
//       // The service returns success: false for invalid tokens
//       // So we should get 200 with success: false
//       expect(res.status).toBe(200);
      
//       const responseData = await res.json();
//       expect(responseData.success).toBe(false);
//     });
//   });

//   describe("Endpoint Availability", () => {
//     it("should have profile GET endpoint", async () => {
//       const res = await client.v1.users.profile.$get();
//       // Should not return 404 (endpoint exists)
//       expect(res.status).not.toBe(404);
//       // Should return 401 for missing auth
//       expect(res.status).toBe(401);
//     });

//     it("should have profile PUT endpoint", async () => {
//       const res = await client.v1.users.profile.$put({
//         json: {
//           first_name: "Test"
//         }
//       });
//       // Should not return 404 (endpoint exists)
//       expect(res.status).not.toBe(404);
//       // Should return 401 for missing auth
//       expect(res.status).toBe(401);
//     });

//     it("should validate request body for PUT /v1/users/profile", async () => {
//       const res = await client.v1.users.profile.$put({
//         json: {
//           new_email: "invalid-email" // Invalid email format
//         }
//       });
//       // Should return 401 for missing auth (auth middleware runs first)
//       expect(res.status).toBe(401);
//     });
//   });
// });