{"id": "3405320c-b0a6-40d1-857f-378a91f3545f", "prevId": "1f39e7e6-5f4e-4d30-8d14-adc660058be1", "version": "7", "dialect": "postgresql", "tables": {"public.files": {"name": "files", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": true}, "uploaded_by": {"name": "uploaded_by", "type": "uuid", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "original_name": {"name": "original_name", "type": "text", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "storage_path": {"name": "storage_path", "type": "text", "primaryKey": false, "notNull": true}, "storage_url": {"name": "storage_url", "type": "text", "primaryKey": false, "notNull": false}, "file_type": {"name": "file_type", "type": "text", "primaryKey": false, "notNull": true}, "entity_type": {"name": "entity_type", "type": "text", "primaryKey": false, "notNull": false}, "entity_id": {"name": "entity_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_files_created_at": {"name": "idx_files_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_files_entity": {"name": "idx_files_entity", "columns": [{"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_files_file_type": {"name": "idx_files_file_type", "columns": [{"expression": "file_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_files_uploaded_by": {"name": "idx_files_uploaded_by", "columns": [{"expression": "uploaded_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_files_workspace_id": {"name": "idx_files_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"files_workspace_id_fkey": {"name": "files_workspace_id_fkey", "tableFrom": "files", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "files_uploaded_by_fkey": {"name": "files_uploaded_by_fkey", "tableFrom": "files", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["uploaded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.listing_details": {"name": "listing_details", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "listing_id": {"name": "listing_id", "type": "uuid", "primaryKey": false, "notNull": true}, "business_description": {"name": "business_description", "type": "text", "primaryKey": false, "notNull": false}, "brief_description": {"name": "brief_description", "type": "text", "primaryKey": false, "notNull": false}, "financial_details": {"name": "financial_details", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\"revenue_2023\":null,\"ebitda\":null,\"assets_included\":[],\"inventory_value\":null,\"additional_financial_info\":{}}'::jsonb"}, "operations": {"name": "operations", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\"business_model\":\"\",\"key_features\":[],\"competitive_advantages\":[],\"operational_details\":{}}'::jsonb"}, "growth_opportunities": {"name": "growth_opportunities", "type": "text[]", "primaryKey": false, "notNull": false}, "reason_for_sale": {"name": "reason_for_sale", "type": "text", "primaryKey": false, "notNull": false}, "training_period": {"name": "training_period", "type": "text", "primaryKey": false, "notNull": false}, "support_type": {"name": "support_type", "type": "text", "primaryKey": false, "notNull": false}, "financing_available": {"name": "financing_available", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "equipment_highlights": {"name": "equipment_highlights", "type": "text[]", "primaryKey": false, "notNull": false}, "supplier_relationships": {"name": "supplier_relationships", "type": "text", "primaryKey": false, "notNull": false}, "real_estate_status": {"name": "real_estate_status", "type": "text", "primaryKey": false, "notNull": false}, "lease_details": {"name": "lease_details", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\"lease_terms\":\"\",\"monthly_rent\":null,\"lease_expiration\":null,\"renewal_options\":\"\",\"landlord_info\":{}}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_listing_details_listing_id": {"name": "idx_listing_details_listing_id", "columns": [{"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listing_details_created_at": {"name": "idx_listing_details_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"listing_details_listing_id_fkey": {"name": "listing_details_listing_id_fkey", "tableFrom": "listing_details", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_listing_details": {"name": "unique_listing_details", "nullsNotDistinct": false, "columns": ["listing_id"]}}}, "public.listing_notes": {"name": "listing_notes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "listing_id": {"name": "listing_id", "type": "uuid", "primaryKey": false, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "mentions": {"name": "mentions", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{\"\"}'"}, "is_private": {"name": "is_private", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_listing_notes_created_at": {"name": "idx_listing_notes_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listing_notes_created_by": {"name": "idx_listing_notes_created_by", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listing_notes_listing_id": {"name": "idx_listing_notes_listing_id", "columns": [{"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listing_notes_workspace_id": {"name": "idx_listing_notes_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"listing_notes_listing_id_fkey": {"name": "listing_notes_listing_id_fkey", "tableFrom": "listing_notes", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "listing_notes_workspace_id_fkey": {"name": "listing_notes_workspace_id_fkey", "tableFrom": "listing_notes", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "listing_notes_created_by_fkey": {"name": "listing_notes_created_by_fkey", "tableFrom": "listing_notes", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.listing_status_history": {"name": "listing_status_history", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "listing_id": {"name": "listing_id", "type": "uuid", "primaryKey": false, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": true}, "changed_by": {"name": "changed_by", "type": "uuid", "primaryKey": false, "notNull": true}, "from_status": {"name": "from_status", "type": "text", "primaryKey": false, "notNull": false}, "to_status": {"name": "to_status", "type": "text", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_listing_status_history_listing_id": {"name": "idx_listing_status_history_listing_id", "columns": [{"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listing_status_history_created_at": {"name": "idx_listing_status_history_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listing_status_history_changed_by": {"name": "idx_listing_status_history_changed_by", "columns": [{"expression": "changed_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listing_status_history_to_status": {"name": "idx_listing_status_history_to_status", "columns": [{"expression": "to_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"listing_status_history_listing_id_fkey": {"name": "listing_status_history_listing_id_fkey", "tableFrom": "listing_status_history", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "listing_status_history_workspace_id_fkey": {"name": "listing_status_history_workspace_id_fkey", "tableFrom": "listing_status_history", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "listing_status_history_changed_by_fkey": {"name": "listing_status_history_changed_by_fkey", "tableFrom": "listing_status_history", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["changed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.listings": {"name": "listings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_to": {"name": "assigned_to", "type": "uuid", "primaryKey": false, "notNull": false}, "business_name": {"name": "business_name", "type": "text", "primaryKey": false, "notNull": true}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": true}, "asking_price": {"name": "asking_price", "type": "numeric(12, 2)", "primaryKey": false, "notNull": false}, "cash_flow_sde": {"name": "cash_flow_sde", "type": "numeric(12, 2)", "primaryKey": false, "notNull": false}, "annual_revenue": {"name": "annual_revenue", "type": "numeric(12, 2)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "general_location": {"name": "general_location", "type": "text", "primaryKey": false, "notNull": false}, "year_established": {"name": "year_established", "type": "integer", "primaryKey": false, "notNull": false}, "employees": {"name": "employees", "type": "integer", "primaryKey": false, "notNull": false}, "owner_hours_week": {"name": "owner_hours_week", "type": "integer", "primaryKey": false, "notNull": false}, "date_listed": {"name": "date_listed", "type": "date", "primaryKey": false, "notNull": false}, "days_listed": {"name": "days_listed", "type": "integer", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(12, 2)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "text", "primaryKey": false, "notNull": false}, "property_type": {"name": "property_type", "type": "text", "primaryKey": false, "notNull": false}, "square_footage": {"name": "square_footage", "type": "integer", "primaryKey": false, "notNull": false}, "lot_size": {"name": "lot_size", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "year_built": {"name": "year_built", "type": "integer", "primaryKey": false, "notNull": false}, "bedrooms": {"name": "bedrooms", "type": "integer", "primaryKey": false, "notNull": false}, "bathrooms": {"name": "bathrooms", "type": "numeric(3, 1)", "primaryKey": false, "notNull": false}, "listing_type": {"name": "listing_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'business_sale'"}, "team_visibility": {"name": "team_visibility", "type": "text", "primaryKey": false, "notNull": false, "default": "'all'"}, "internal_notes": {"name": "internal_notes", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "photos": {"name": "photos", "type": "text[]", "primaryKey": false, "notNull": false}, "documents": {"name": "documents", "type": "text[]", "primaryKey": false, "notNull": false}, "featured_photo": {"name": "featured_photo", "type": "text", "primaryKey": false, "notNull": false}, "virtual_tour_url": {"name": "virtual_tour_url", "type": "text", "primaryKey": false, "notNull": false}, "mls_number": {"name": "mls_number", "type": "text", "primaryKey": false, "notNull": false}, "listing_date": {"name": "listing_date", "type": "date", "primaryKey": false, "notNull": false}, "expiration_date": {"name": "expiration_date", "type": "date", "primaryKey": false, "notNull": false}, "days_on_market": {"name": "days_on_market", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_listings_assigned_to": {"name": "idx_listings_assigned_to", "columns": [{"expression": "assigned_to", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_city_state": {"name": "idx_listings_city_state", "columns": [{"expression": "city", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "state", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_created_at": {"name": "idx_listings_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_created_by": {"name": "idx_listings_created_by", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_listing_type": {"name": "idx_listings_listing_type", "columns": [{"expression": "listing_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_price": {"name": "idx_listings_price", "columns": [{"expression": "price", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_asking_price": {"name": "idx_listings_asking_price", "columns": [{"expression": "asking_price", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_status": {"name": "idx_listings_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_workspace_id": {"name": "idx_listings_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_industry": {"name": "idx_listings_industry", "columns": [{"expression": "industry", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_date_listed": {"name": "idx_listings_date_listed", "columns": [{"expression": "date_listed", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"listings_workspace_id_fkey": {"name": "listings_workspace_id_fkey", "tableFrom": "listings", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "listings_created_by_fkey": {"name": "listings_created_by_fkey", "tableFrom": "listings", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "listings_assigned_to_fkey": {"name": "listings_assigned_to_fkey", "tableFrom": "listings", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["assigned_to"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "entity_type": {"name": "entity_type", "type": "text", "primaryKey": false, "notNull": false}, "entity_id": {"name": "entity_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "read_at": {"name": "read_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": false, "default": "'normal'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_notifications_created_at": {"name": "idx_notifications_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_entity": {"name": "idx_notifications_entity", "columns": [{"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_is_read": {"name": "idx_notifications_is_read", "columns": [{"expression": "is_read", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_priority": {"name": "idx_notifications_priority", "columns": [{"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_type": {"name": "idx_notifications_type", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_user_id": {"name": "idx_notifications_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_workspace_id": {"name": "idx_notifications_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"notifications_workspace_id_fkey": {"name": "notifications_workspace_id_fkey", "tableFrom": "notifications", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "notifications_user_id_fkey": {"name": "notifications_user_id_fkey", "tableFrom": "notifications", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.user_profiles": {"name": "user_profiles", "schema": "", "columns": {"display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "license_number": {"name": "license_number", "type": "text", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "specialties": {"name": "specialties", "type": "text[]", "primaryKey": false, "notNull": false}, "invited_at": {"name": "invited_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "joined_at": {"name": "joined_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "invited_by": {"name": "invited_by", "type": "uuid", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\"notifications\":{\"email_notifications\":true,\"push_notifications\":true,\"listing_updates\":true,\"team_updates\":true,\"system_updates\":true},\"display\":{\"timezone\":\"America/New_York\",\"date_format\":\"MM/DD/YYYY\",\"currency\":\"USD\",\"language\":\"en\"},\"privacy\":{\"profile_visibility\":\"team\",\"contact_visibility\":\"team\"}}'::jsonb"}, "last_login_at": {"name": "last_login_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"idx_user_profiles_active": {"name": "idx_user_profiles_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "(is_active = true)", "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_email": {"name": "idx_user_profiles_email", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_role": {"name": "idx_user_profiles_role", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_user_id": {"name": "idx_user_profiles_user_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_workspace_id": {"name": "idx_user_profiles_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_profiles_workspace_id_fkey": {"name": "user_profiles_workspace_id_fkey", "tableFrom": "user_profiles", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_profiles_user_id_fkey": {"name": "user_profiles_user_id_fkey", "tableFrom": "user_profiles", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_workspace_email": {"name": "unique_workspace_email", "nullsNotDistinct": false, "columns": ["workspace_id", "email"]}}}, "auth.users": {"name": "users", "schema": "auth", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.workspace_invitations": {"name": "workspace_invitations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "invited_by": {"name": "invited_by", "type": "uuid", "primaryKey": false, "notNull": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "accepted_at": {"name": "accepted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_workspace_invitations_email": {"name": "idx_workspace_invitations_email", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspace_invitations_expires_at": {"name": "idx_workspace_invitations_expires_at", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspace_invitations_token": {"name": "idx_workspace_invitations_token", "columns": [{"expression": "token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspace_invitations_workspace_id": {"name": "idx_workspace_invitations_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workspace_invitations_workspace_id_fkey": {"name": "workspace_invitations_workspace_id_fkey", "tableFrom": "workspace_invitations", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workspace_invitations_invited_by_fkey": {"name": "workspace_invitations_invited_by_fkey", "tableFrom": "workspace_invitations", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"workspace_invitations_workspace_id_email_key": {"name": "workspace_invitations_workspace_id_email_key", "nullsNotDistinct": false, "columns": ["workspace_id", "email"]}, "workspace_invitations_token_key": {"name": "workspace_invitations_token_key", "nullsNotDistinct": false, "columns": ["token"]}}}, "public.workspaces": {"name": "workspaces", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "company_name": {"name": "company_name", "type": "text", "primaryKey": false, "notNull": true}, "company_type": {"name": "company_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'team'"}, "subscription_plan": {"name": "subscription_plan", "type": "text", "primaryKey": false, "notNull": false, "default": "'trial'"}, "domain": {"name": "domain", "type": "text", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": false}, "primary_color": {"name": "primary_color", "type": "text", "primaryKey": false, "notNull": false, "default": "'#3B82F6'"}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "license_number": {"name": "license_number", "type": "text", "primaryKey": false, "notNull": false}, "specialties": {"name": "specialties", "type": "text[]", "primaryKey": false, "notNull": false}, "target_markets": {"name": "target_markets", "type": "text[]", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'trial'"}, "trial_ends_at": {"name": "trial_ends_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "(now() + '14 days'::interval)"}, "onboarding_completed": {"name": "onboarding_completed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "onboarding_step": {"name": "onboarding_step", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_workspaces_status": {"name": "idx_workspaces_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspaces_subscription_plan": {"name": "idx_workspaces_subscription_plan", "columns": [{"expression": "subscription_plan", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspaces_trial_ends_at": {"name": "idx_workspaces_trial_ends_at", "columns": [{"expression": "trial_ends_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "(status = 'trial'::text)", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"workspaces_domain_key": {"name": "workspaces_domain_key", "nullsNotDistinct": false, "columns": ["domain"]}}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}