{"id": "1e1f2b84-cd61-4312-935c-3ffc8dbdc9c2", "prevId": "38c4039a-0cd9-4faf-bf46-5bca397726ad", "version": "7", "dialect": "postgresql", "tables": {"public._usage_examples": {"name": "_usage_examples", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "done": {"name": "done", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.files": {"name": "files", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": true}, "uploaded_by": {"name": "uploaded_by", "type": "uuid", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "original_name": {"name": "original_name", "type": "text", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "storage_path": {"name": "storage_path", "type": "text", "primaryKey": false, "notNull": true}, "storage_url": {"name": "storage_url", "type": "text", "primaryKey": false, "notNull": false}, "file_type": {"name": "file_type", "type": "text", "primaryKey": false, "notNull": true}, "entity_type": {"name": "entity_type", "type": "text", "primaryKey": false, "notNull": false}, "entity_id": {"name": "entity_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_files_created_at": {"name": "idx_files_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_files_entity": {"name": "idx_files_entity", "columns": [{"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_files_file_type": {"name": "idx_files_file_type", "columns": [{"expression": "file_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_files_uploaded_by": {"name": "idx_files_uploaded_by", "columns": [{"expression": "uploaded_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_files_workspace_id": {"name": "idx_files_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"files_workspace_id_fkey": {"name": "files_workspace_id_fkey", "tableFrom": "files", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "files_uploaded_by_fkey": {"name": "files_uploaded_by_fkey", "tableFrom": "files", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["uploaded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.listing_notes": {"name": "listing_notes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "listing_id": {"name": "listing_id", "type": "uuid", "primaryKey": false, "notNull": true}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "mentions": {"name": "mentions", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{\"\"}'"}, "is_private": {"name": "is_private", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_listing_notes_created_at": {"name": "idx_listing_notes_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listing_notes_created_by": {"name": "idx_listing_notes_created_by", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listing_notes_listing_id": {"name": "idx_listing_notes_listing_id", "columns": [{"expression": "listing_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listing_notes_workspace_id": {"name": "idx_listing_notes_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"listing_notes_listing_id_fkey": {"name": "listing_notes_listing_id_fkey", "tableFrom": "listing_notes", "tableTo": "listings", "columnsFrom": ["listing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "listing_notes_workspace_id_fkey": {"name": "listing_notes_workspace_id_fkey", "tableFrom": "listing_notes", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "listing_notes_created_by_fkey": {"name": "listing_notes_created_by_fkey", "tableFrom": "listing_notes", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.listings": {"name": "listings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_to": {"name": "assigned_to", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(12, 2)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "text", "primaryKey": false, "notNull": false}, "property_type": {"name": "property_type", "type": "text", "primaryKey": false, "notNull": false}, "square_footage": {"name": "square_footage", "type": "integer", "primaryKey": false, "notNull": false}, "lot_size": {"name": "lot_size", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "year_built": {"name": "year_built", "type": "integer", "primaryKey": false, "notNull": false}, "bedrooms": {"name": "bedrooms", "type": "integer", "primaryKey": false, "notNull": false}, "bathrooms": {"name": "bathrooms", "type": "numeric(3, 1)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "listing_type": {"name": "listing_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'sale'"}, "team_visibility": {"name": "team_visibility", "type": "text", "primaryKey": false, "notNull": false, "default": "'all'"}, "internal_notes": {"name": "internal_notes", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "photos": {"name": "photos", "type": "text[]", "primaryKey": false, "notNull": false}, "documents": {"name": "documents", "type": "text[]", "primaryKey": false, "notNull": false}, "featured_photo": {"name": "featured_photo", "type": "text", "primaryKey": false, "notNull": false}, "virtual_tour_url": {"name": "virtual_tour_url", "type": "text", "primaryKey": false, "notNull": false}, "mls_number": {"name": "mls_number", "type": "text", "primaryKey": false, "notNull": false}, "listing_date": {"name": "listing_date", "type": "date", "primaryKey": false, "notNull": false}, "expiration_date": {"name": "expiration_date", "type": "date", "primaryKey": false, "notNull": false}, "days_on_market": {"name": "days_on_market", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_listings_assigned_to": {"name": "idx_listings_assigned_to", "columns": [{"expression": "assigned_to", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_city_state": {"name": "idx_listings_city_state", "columns": [{"expression": "city", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "state", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_created_at": {"name": "idx_listings_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_created_by": {"name": "idx_listings_created_by", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_listing_type": {"name": "idx_listings_listing_type", "columns": [{"expression": "listing_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_price": {"name": "idx_listings_price", "columns": [{"expression": "price", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_status": {"name": "idx_listings_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_listings_workspace_id": {"name": "idx_listings_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"listings_workspace_id_fkey": {"name": "listings_workspace_id_fkey", "tableFrom": "listings", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "entity_type": {"name": "entity_type", "type": "text", "primaryKey": false, "notNull": false}, "entity_id": {"name": "entity_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "read_at": {"name": "read_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": false, "default": "'normal'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_notifications_created_at": {"name": "idx_notifications_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_entity": {"name": "idx_notifications_entity", "columns": [{"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_is_read": {"name": "idx_notifications_is_read", "columns": [{"expression": "is_read", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_priority": {"name": "idx_notifications_priority", "columns": [{"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_type": {"name": "idx_notifications_type", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_user_id": {"name": "idx_notifications_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_notifications_workspace_id": {"name": "idx_notifications_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"notifications_workspace_id_fkey": {"name": "notifications_workspace_id_fkey", "tableFrom": "notifications", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "notifications_user_id_fkey": {"name": "notifications_user_id_fkey", "tableFrom": "notifications", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.user_profiles": {"name": "user_profiles", "schema": "", "columns": {"display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "license_number": {"name": "license_number", "type": "text", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "specialties": {"name": "specialties", "type": "text[]", "primaryKey": false, "notNull": false}, "invited_at": {"name": "invited_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "joined_at": {"name": "joined_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "invited_by": {"name": "invited_by", "type": "uuid", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"idx_user_profiles_active": {"name": "idx_user_profiles_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "(is_active = true)", "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_email": {"name": "idx_user_profiles_email", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_role": {"name": "idx_user_profiles_role", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_user_id": {"name": "idx_user_profiles_user_id", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_profiles_workspace_id": {"name": "idx_user_profiles_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_profiles_workspace_id_fkey": {"name": "user_profiles_workspace_id_fkey", "tableFrom": "user_profiles", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_profiles_user_id_fkey": {"name": "user_profiles_user_id_fkey", "tableFrom": "user_profiles", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_workspace_email": {"name": "unique_workspace_email", "nullsNotDistinct": false, "columns": ["workspace_id", "email"]}}}, "auth.users": {"name": "users", "schema": "auth", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.workspace_invitations": {"name": "workspace_invitations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "workspace_id": {"name": "workspace_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "invited_by": {"name": "invited_by", "type": "uuid", "primaryKey": false, "notNull": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "accepted_at": {"name": "accepted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_workspace_invitations_email": {"name": "idx_workspace_invitations_email", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspace_invitations_expires_at": {"name": "idx_workspace_invitations_expires_at", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspace_invitations_token": {"name": "idx_workspace_invitations_token", "columns": [{"expression": "token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspace_invitations_workspace_id": {"name": "idx_workspace_invitations_workspace_id", "columns": [{"expression": "workspace_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workspace_invitations_workspace_id_fkey": {"name": "workspace_invitations_workspace_id_fkey", "tableFrom": "workspace_invitations", "tableTo": "workspaces", "columnsFrom": ["workspace_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workspace_invitations_invited_by_fkey": {"name": "workspace_invitations_invited_by_fkey", "tableFrom": "workspace_invitations", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"workspace_invitations_workspace_id_email_key": {"name": "workspace_invitations_workspace_id_email_key", "nullsNotDistinct": false, "columns": ["workspace_id", "email"]}, "workspace_invitations_token_key": {"name": "workspace_invitations_token_key", "nullsNotDistinct": false, "columns": ["token"]}}}, "public.workspaces": {"name": "workspaces", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "company_name": {"name": "company_name", "type": "text", "primaryKey": false, "notNull": true}, "company_type": {"name": "company_type", "type": "text", "primaryKey": false, "notNull": false, "default": "'team'"}, "subscription_plan": {"name": "subscription_plan", "type": "text", "primaryKey": false, "notNull": false, "default": "'trial'"}, "domain": {"name": "domain", "type": "text", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": false}, "primary_color": {"name": "primary_color", "type": "text", "primaryKey": false, "notNull": false, "default": "'#3B82F6'"}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "license_number": {"name": "license_number", "type": "text", "primaryKey": false, "notNull": false}, "specialties": {"name": "specialties", "type": "text[]", "primaryKey": false, "notNull": false}, "target_markets": {"name": "target_markets", "type": "text[]", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'trial'"}, "trial_ends_at": {"name": "trial_ends_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "(now() + '14 days'::interval)"}, "onboarding_completed": {"name": "onboarding_completed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "onboarding_step": {"name": "onboarding_step", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_workspaces_status": {"name": "idx_workspaces_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspaces_subscription_plan": {"name": "idx_workspaces_subscription_plan", "columns": [{"expression": "subscription_plan", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_workspaces_trial_ends_at": {"name": "idx_workspaces_trial_ends_at", "columns": [{"expression": "trial_ends_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "(status = 'trial'::text)", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"workspaces_domain_key": {"name": "workspaces_domain_key", "nullsNotDistinct": false, "columns": ["domain"]}}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}