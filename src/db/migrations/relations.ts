import { relations } from "drizzle-orm/relations";
import { listings, listingNotes, workspaces, usersInAuth, files, workspaceInvitations, notifications, userProfiles, listingDetails, listingStatusHistory, log } from "./schema";

export const listingNotesRelations = relations(listingNotes, ({one}) => ({
	listing: one(listings, {
		fields: [listingNotes.listingId],
		references: [listings.id]
	}),
	workspace: one(workspaces, {
		fields: [listingNotes.workspaceId],
		references: [workspaces.id]
	}),
	usersInAuth: one(usersInAuth, {
		fields: [listingNotes.createdBy],
		references: [usersInAuth.id]
	}),
}));

export const listingsRelations = relations(listings, ({one, many}) => ({
	listingNotes: many(listingNotes),
	workspace: one(workspaces, {
		fields: [listings.workspaceId],
		references: [workspaces.id]
	}),
	usersInAuth_createdBy: one(usersInAuth, {
		fields: [listings.createdBy],
		references: [usersInAuth.id],
		relationName: "listings_createdBy_usersInAuth_id"
	}),
	usersInAuth_assignedTo: one(usersInAuth, {
		fields: [listings.assignedTo],
		references: [usersInAuth.id],
		relationName: "listings_assignedTo_usersInAuth_id"
	}),
	listingDetails: many(listingDetails),
	listingStatusHistories: many(listingStatusHistory),
}));

export const workspacesRelations = relations(workspaces, ({many}) => ({
	listingNotes: many(listingNotes),
	files: many(files),
	listings: many(listings),
	workspaceInvitations: many(workspaceInvitations),
	notifications: many(notifications),
	userProfiles: many(userProfiles),
	listingStatusHistories: many(listingStatusHistory),
	logs: many(log),
}));

export const usersInAuthRelations = relations(usersInAuth, ({many}) => ({
	listingNotes: many(listingNotes),
	files: many(files),
	listings_createdBy: many(listings, {
		relationName: "listings_createdBy_usersInAuth_id"
	}),
	listings_assignedTo: many(listings, {
		relationName: "listings_assignedTo_usersInAuth_id"
	}),
	workspaceInvitations: many(workspaceInvitations),
	notifications: many(notifications),
	userProfiles: many(userProfiles),
	listingStatusHistories: many(listingStatusHistory),
	logs: many(log),
}));

export const filesRelations = relations(files, ({one}) => ({
	workspace: one(workspaces, {
		fields: [files.workspaceId],
		references: [workspaces.id]
	}),
	usersInAuth: one(usersInAuth, {
		fields: [files.uploadedBy],
		references: [usersInAuth.id]
	}),
}));

export const workspaceInvitationsRelations = relations(workspaceInvitations, ({one}) => ({
	workspace: one(workspaces, {
		fields: [workspaceInvitations.workspaceId],
		references: [workspaces.id]
	}),
	usersInAuth: one(usersInAuth, {
		fields: [workspaceInvitations.invitedBy],
		references: [usersInAuth.id]
	}),
}));

export const notificationsRelations = relations(notifications, ({one}) => ({
	workspace: one(workspaces, {
		fields: [notifications.workspaceId],
		references: [workspaces.id]
	}),
	usersInAuth: one(usersInAuth, {
		fields: [notifications.userId],
		references: [usersInAuth.id]
	}),
}));

export const userProfilesRelations = relations(userProfiles, ({one}) => ({
	workspace: one(workspaces, {
		fields: [userProfiles.workspaceId],
		references: [workspaces.id]
	}),
	usersInAuth: one(usersInAuth, {
		fields: [userProfiles.userId],
		references: [usersInAuth.id]
	}),
}));

export const listingDetailsRelations = relations(listingDetails, ({one}) => ({
	listing: one(listings, {
		fields: [listingDetails.listingId],
		references: [listings.id]
	}),
}));

export const listingStatusHistoryRelations = relations(listingStatusHistory, ({one}) => ({
	listing: one(listings, {
		fields: [listingStatusHistory.listingId],
		references: [listings.id]
	}),
	workspace: one(workspaces, {
		fields: [listingStatusHistory.workspaceId],
		references: [workspaces.id]
	}),
	usersInAuth: one(usersInAuth, {
		fields: [listingStatusHistory.changedBy],
		references: [usersInAuth.id]
	}),
}));

export const logRelations = relations(log, ({one}) => ({
	usersInAuth: one(usersInAuth, {
		fields: [log.userId],
		references: [usersInAuth.id]
	}),
	workspace: one(workspaces, {
		fields: [log.workspaceId],
		references: [workspaces.id]
	}),
}));