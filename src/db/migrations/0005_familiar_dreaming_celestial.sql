CREATE TABLE IF NOT EXISTS "_log" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"method" text NOT NULL,
	"url" text NOT NULL,
	"path" text NOT NULL,
	"user_agent" text,
	"ip_address" text,
	"user_id" uuid,
	"workspace_id" uuid,
	"headers" jsonb,
	"query_params" jsonb,
	"request_body" jsonb,
	"status_code" integer,
	"response_body" jsonb,
	"response_headers" jsonb,
	"start_time" timestamp with time zone NOT NULL,
	"end_time" timestamp with time zone,
	"duration" integer,
	"error_message" text,
	"error_stack" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "_log" ADD CONSTRAINT "api_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "_log" ADD CONSTRAINT "api_logs_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "public"."workspaces"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_api_logs_created_at" ON "_log" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_api_logs_user_id" ON "_log" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_api_logs_workspace_id" ON "_log" USING btree ("workspace_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_api_logs_status_code" ON "_log" USING btree ("status_code");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_api_logs_method" ON "_log" USING btree ("method");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_api_logs_path" ON "_log" USING btree ("path");