CREATE TABLE IF NOT EXISTS "listing_details" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"listing_id" uuid NOT NULL,
	"business_description" text,
	"brief_description" text,
	"financial_details" jsonb DEFAULT '{"revenue_2023":null,"ebitda":null,"assets_included":[],"inventory_value":null,"additional_financial_info":{}}'::jsonb,
	"operations" jsonb DEFAULT '{"business_model":"","key_features":[],"competitive_advantages":[],"operational_details":{}}'::jsonb,
	"growth_opportunities" text[],
	"reason_for_sale" text,
	"training_period" text,
	"support_type" text,
	"financing_available" boolean DEFAULT false,
	"equipment_highlights" text[],
	"supplier_relationships" text,
	"real_estate_status" text,
	"lease_details" jsonb DEFAULT '{"lease_terms":"","monthly_rent":null,"lease_expiration":null,"renewal_options":"","landlord_info":{}}'::jsonb,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "unique_listing_details" UNIQUE("listing_id")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "listing_status_history" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"listing_id" uuid NOT NULL,
	"workspace_id" uuid NOT NULL,
	"changed_by" uuid NOT NULL,
	"from_status" text,
	"to_status" text NOT NULL,
	"reason" text,
	"notes" text,
	"metadata" jsonb DEFAULT '{}'::jsonb,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "listings" ALTER COLUMN "title" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "listings" ALTER COLUMN "listing_type" SET DEFAULT 'business_sale';--> statement-breakpoint
ALTER TABLE "listings" ADD COLUMN "business_name" text NOT NULL;--> statement-breakpoint
ALTER TABLE "listings" ADD COLUMN "industry" text NOT NULL;--> statement-breakpoint
ALTER TABLE "listings" ADD COLUMN "asking_price" numeric(12, 2);--> statement-breakpoint
ALTER TABLE "listings" ADD COLUMN "cash_flow_sde" numeric(12, 2);--> statement-breakpoint
ALTER TABLE "listings" ADD COLUMN "annual_revenue" numeric(12, 2);--> statement-breakpoint
ALTER TABLE "listings" ADD COLUMN "general_location" text;--> statement-breakpoint
ALTER TABLE "listings" ADD COLUMN "year_established" integer;--> statement-breakpoint
ALTER TABLE "listings" ADD COLUMN "employees" integer;--> statement-breakpoint
ALTER TABLE "listings" ADD COLUMN "owner_hours_week" integer;--> statement-breakpoint
ALTER TABLE "listings" ADD COLUMN "date_listed" date;--> statement-breakpoint
ALTER TABLE "listings" ADD COLUMN "days_listed" integer;--> statement-breakpoint
ALTER TABLE "user_profiles" ADD COLUMN "preferences" jsonb DEFAULT '{"notifications":{"email_notifications":true,"push_notifications":true,"listing_updates":true,"team_updates":true,"system_updates":true},"display":{"timezone":"America/New_York","date_format":"MM/DD/YYYY","currency":"USD","language":"en"},"privacy":{"profile_visibility":"team","contact_visibility":"team"}}'::jsonb;--> statement-breakpoint
ALTER TABLE "user_profiles" ADD COLUMN "last_login_at" timestamp with time zone;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "listing_details" ADD CONSTRAINT "listing_details_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "public"."listings"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "listing_status_history" ADD CONSTRAINT "listing_status_history_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "public"."listings"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "listing_status_history" ADD CONSTRAINT "listing_status_history_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "public"."workspaces"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "listing_status_history" ADD CONSTRAINT "listing_status_history_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "auth"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_listing_details_listing_id" ON "listing_details" USING btree ("listing_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_listing_details_created_at" ON "listing_details" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_listing_status_history_listing_id" ON "listing_status_history" USING btree ("listing_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_listing_status_history_created_at" ON "listing_status_history" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_listing_status_history_changed_by" ON "listing_status_history" USING btree ("changed_by");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_listing_status_history_to_status" ON "listing_status_history" USING btree ("to_status");--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "listings" ADD CONSTRAINT "listings_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "listings" ADD CONSTRAINT "listings_assigned_to_fkey" FOREIGN KEY ("assigned_to") REFERENCES "auth"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_listings_asking_price" ON "listings" USING btree ("asking_price");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_listings_industry" ON "listings" USING btree ("industry");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_listings_date_listed" ON "listings" USING btree ("date_listed");