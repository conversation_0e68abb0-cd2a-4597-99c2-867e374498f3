import { createClient } from "@supabase/supabase-js";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";

import env from "@/env";

import * as schema from "./schema";

// PostgreSQL client for Drizzle ORM
const client = postgres(env.DATABASE_URL);

const db = drizzle(client, {
  schema,
});

// Supabase client for authentication
const supabase = createClient(env.SUPABASE_URL, env.SUPABASE_SERVICE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

export default db;
export { supabase };
